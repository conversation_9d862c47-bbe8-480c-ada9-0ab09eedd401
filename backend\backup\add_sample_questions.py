import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cati_system.settings')
django.setup()

from interviews.models import Question

def create_sample_questions():
    """Create sample questions for testing"""
    
    # Clear existing questions
    Question.objects.all().delete()
    
    # Sample questions for Round 1
    questions_data = [
        {
            'text': 'What is your name?',
            'type': 'text',
            'stage': 1,
            'order': 1,
            'required': True,
            'round': 1
        },
        {
            'text': 'How old are you?',
            'type': 'text',
            'stage': 1,
            'order': 2,
            'required': True,
            'round': 1
        },
        {
            'text': 'Are you satisfied with the service?',
            'type': 'boolean',
            'stage': 1,
            'order': 3,
            'required': True,
            'round': 1
        },
        {
            'text': 'Rate your overall experience (1-10)',
            'type': 'scale',
            'stage': 1,
            'order': 4,
            'required': True,
            'round': 1
        },
        {
            'text': 'Which method are you currently using?',
            'type': 'multiple_choice',
            'stage': 1,
            'order': 5,
            'required': True,
            'options': ['Pills', 'Injection', 'Implant', 'IUD', 'Condoms', 'Other'],
            'round': 1
        },
        
        # Common questions for all rounds
        {
            'text': 'Do you have any concerns about your current method?',
            'type': 'boolean',
            'stage': 1,
            'order': 6,
            'required': True,
            'round': None  # Available for all rounds
        },
        {
            'text': 'Would you recommend this service to others?',
            'type': 'boolean',
            'stage': 1,
            'order': 7,
            'required': True,
            'round': None  # Available for all rounds
        },
        
        # Round 2 specific questions
        {
            'text': 'Have you experienced any side effects since your last visit?',
            'type': 'boolean',
            'stage': 1,
            'order': 8,
            'required': True,
            'round': 2
        },
        {
            'text': 'Are you still using the same contraceptive method?',
            'type': 'boolean',
            'stage': 1,
            'order': 9,
            'required': True,
            'round': 2
        },
    ]
    
    created_questions = []
    for q_data in questions_data:
        question = Question.objects.create(**q_data)
        created_questions.append(question)
        print(f"Created question: {question.text} (Round: {question.round or 'All'})")
    
    print(f"\nCreated {len(created_questions)} sample questions")
    return created_questions

if __name__ == '__main__':
    create_sample_questions()
