#!/usr/bin/env python
"""
<PERSON>ript to create test users for the CATI system.
Run this after setting up the Django project and running migrations.

Usage: python manage.py shell < create_test_users.py
"""

from accounts.models import User

# Test user credentials
test_users = [
    {
        'username': 'admin',
        'email': '<EMAIL>',
        'password': 'admin123',
        'role': 'admin',
        'phone': '******-0001'
    },
    {
        'username': 'interviewer1',
        'email': '<EMAIL>',
        'password': 'interview123',
        'role': 'interviewer',
        'phone': '******-0002'
    },
    {
        'username': 'interviewer2',
        'email': '<EMAIL>',
        'password': 'interview123',
        'role': 'interviewer',
        'phone': '******-0003'
    },
    {
        'username': 'contact1',
        'email': '<EMAIL>',
        'password': 'contact123',
        'role': 'contact',
        'phone': '******-1001'
    },
    {
        'username': 'contact2',
        'email': '<EMAIL>',
        'password': 'contact123',
        'role': 'contact',
        'phone': '******-1002'
    }
]

print("Creating test users...")

for user_data in test_users:
    username = user_data['username']
    
    # Check if user already exists
    if User.objects.filter(username=username).exists():
        print(f"User '{username}' already exists, skipping...")
        continue
    
    # Create user
    user = User.objects.create_user(
        username=user_data['username'],
        email=user_data['email'],
        password=user_data['password'],
        role=user_data['role'],
        phone=user_data['phone']
    )
    
    # Make admin users staff
    if user_data['role'] == 'admin':
        user.is_staff = True
        user.is_superuser = True
        user.save()
    
    print(f"Created {user_data['role']} user: {username}")

print("\nTest users created successfully!")
print("\n=== LOGIN CREDENTIALS ===")
print("ADMIN:")
print("  Username: admin")
print("  Password: admin123")
print("\nINTERVIEWER 1:")
print("  Username: interviewer1")
print("  Password: interview123")
print("\nINTERVIEWER 2:")
print("  Username: interviewer2")
print("  Password: interview123")
print("\nNOTE: Contact users cannot login to the system.")
print("They are for reference/testing purposes only.")