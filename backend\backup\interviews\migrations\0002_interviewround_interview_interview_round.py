# Generated by Django 5.2.3 on 2025-06-20 02:45

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contacts', '0003_alter_contact_status'),
        ('interviews', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='InterviewRound',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('round_number', models.IntegerField(choices=[(1, 'Round 1'), (2, 'Round 2'), (3, 'Round 3'), (4, 'Round 4')])),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('active', 'Active'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('scheduled_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('contact', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interview_rounds', to='contacts.contact')),
            ],
            options={
                'ordering': ['contact', 'round_number'],
                'unique_together': {('contact', 'round_number')},
            },
        ),
        migrations.AddField(
            model_name='interview',
            name='interview_round',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='interviews', to='interviews.interviewround'),
        ),
    ]
