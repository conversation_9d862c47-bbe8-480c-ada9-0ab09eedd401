# Generated by Django 5.2.3 on 2025-06-19 21:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('phone', models.CharField(max_length=20)),
                ('serialNumber', models.CharField(blank=True, max_length=100, null=True)),
                ('cuid', models.CharField(blank=True, max_length=100, null=True)),
                ('ticketNumber', models.CharField(blank=True, max_length=100, null=True)),
                ('location', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('status', models.CharField(choices=[('not started', 'Not started'), ('1', 'Round 1'), ('2', 'Round 2'), ('3', 'Round 3'), ('4', 'Round 4')], default='not started', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_contact', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contacts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('phone', 'created_by')},
            },
        ),
    ]
