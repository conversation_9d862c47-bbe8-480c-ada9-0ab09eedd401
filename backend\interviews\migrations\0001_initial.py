# Generated by Django 5.2.3 on 2025-06-19 21:40

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contacts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField()),
                ('type', models.CharField(choices=[('text', 'Text'), ('multiple_choice', 'Multiple Choice'), ('scale', 'Scale (1-10)'), ('boolean', 'Yes/No')], max_length=20)),
                ('stage', models.IntegerField(default=1)),
                ('options', models.JSONField(blank=True, null=True)),
                ('routing_logic', models.JSONField(blank=True, null=True)),
                ('required', models.BooleanField(default=True)),
                ('order', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['stage', 'order'],
            },
        ),
        migrations.CreateModel(
            name='Interview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stage', models.IntegerField(default=1)),
                ('status', models.CharField(choices=[('in_progress', 'In Progress'), ('completed', 'Completed'), ('paused', 'Paused')], default='in_progress', max_length=20)),
                ('current_question_index', models.IntegerField(default=0)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('contact', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interviews', to='contacts.contact')),
                ('interviewer', models.ForeignKey(limit_choices_to={'role__in': ['interviewer', 'admin']}, on_delete=django.db.models.deletion.CASCADE, related_name='interviews', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='Response',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('answer', models.JSONField()),
                ('completed_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('interview', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='interviews.interview')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='interviews.question')),
            ],
            options={
                'ordering': ['completed_at'],
                'unique_together': {('interview', 'question')},
            },
        ),
    ]
