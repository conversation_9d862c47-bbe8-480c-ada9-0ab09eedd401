{"name": "cati-system-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@mdi/font": "^7.4.47", "axios": "^1.6.2", "pinia": "^2.1.7", "vue": "^3.4.38", "vue-router": "^4.2.5", "vuetify": "^3.8.10"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.3", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.5.3", "vite": "^5.4.19", "vue-tsc": "^2.1.4"}}