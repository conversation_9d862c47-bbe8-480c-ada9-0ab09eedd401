#!/usr/bin/env python3
"""
Test script to validate auto-population functionality fixes
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cati_system.settings')
django.setup()

from interviews.models import Question, Interview, InterviewRound
from contacts.models import Contact
from django.contrib.auth import get_user_model

User = get_user_model()

def test_auto_population_setup():
    """Test that the setup for auto-population is correct"""
    
    print("🧪 Testing Auto-Population Setup")
    print("=" * 50)
    
    # Check if we have questions
    questions = Question.objects.all()
    print(f"📋 Total questions in database: {questions.count()}")
    
    for question in questions:
        print(f"  - ID: {question.id}, Text: '{question.text}', Type: {question.type}, Round: {question.round}")
    
    # Check if we have contacts
    contacts = Contact.objects.all()
    print(f"\n👥 Total contacts in database: {contacts.count()}")
    
    for contact in contacts[:3]:  # Show first 3 contacts
        print(f"  - ID: {contact.id}, Name: '{contact.name}', Phone: {contact.phone}")
    
    # Test keyword matching logic
    print(f"\n🔍 Testing Keyword Matching Logic")
    print("-" * 30)
    
    # Define the same field mappings as in the frontend
    field_mappings = [
        {
            'field': 'name',
            'keywords': ['name', 'full name', 'contact name', 'your name', 'participant name', 'what is your name']
        },
        {
            'field': 'phone',
            'keywords': ['phone', 'contact number', 'telephone', 'mobile', 'phone number', 'contact info']
        },
        {
            'field': 'serialNumber',
            'keywords': ['serial number', 'serial', 'serial no', 'device serial', 'product serial']
        },
        {
            'field': 'cuid',
            'keywords': ['cuid', 'customer id', 'unique id', 'customer identifier', 'user id']
        },
        {
            'field': 'ticketNumber',
            'keywords': ['ticket number', 'ticket', 'ticket no', 'reference number', 'case number']
        },
    ]
    
    # Test matching for each field
    for mapping in field_mappings:
        matching_questions = []
        for question in questions:
            question_text = question.text.lower()
            matches = any(keyword.lower() in question_text for keyword in mapping['keywords'])
            if matches:
                matching_questions.append(question)
        
        print(f"  {mapping['field']}: Found {len(matching_questions)} matching questions")
        for q in matching_questions:
            print(f"    - '{q.text}' (ID: {q.id}, Round: {q.round})")
    
    print(f"\n✅ Auto-population setup test completed!")
    
    return {
        'questions_count': questions.count(),
        'contacts_count': contacts.count(),
        'questions': list(questions),
        'field_mappings': field_mappings
    }

def create_test_data():
    """Create test data if needed"""
    
    print("\n🔧 Creating Test Data")
    print("=" * 30)
    
    # Create a test user if needed
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={'email': '<EMAIL>'}
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"📋 Using existing test user: {user.username}")
    
    # Create a test contact if needed
    contact, created = Contact.objects.get_or_create(
        name='John Doe',
        defaults={
            'phone': '+1234567890',
            'email': '<EMAIL>',
            'status': 'not_started',
            'cuid': 'CUID123456',
            'serialNumber': 'SN789012',
            'ticketNumber': 'TKT345678',
            'location': 'New York'
        }
    )
    if created:
        print(f"✅ Created test contact: {contact.name}")
    else:
        print(f"📋 Using existing test contact: {contact.name}")
    
    return {
        'user': user,
        'contact': contact
    }

def main():
    """Main test function"""
    
    print("🚀 CATI Auto-Population Test Suite")
    print("=" * 60)
    
    # Test setup
    setup_results = test_auto_population_setup()
    
    # Create test data
    test_data = create_test_data()
    
    print(f"\n📊 Test Summary")
    print("=" * 20)
    print(f"Questions available: {setup_results['questions_count']}")
    print(f"Contacts available: {setup_results['contacts_count']}")
    print(f"Test user: {test_data['user'].username}")
    print(f"Test contact: {test_data['contact'].name}")
    
    print(f"\n🎯 Next Steps:")
    print("1. Start the Django server: python backend/manage.py runserver")
    print("2. Start the Vue.js frontend: npm run dev")
    print("3. Navigate to the interview page for the test contact")
    print("4. Check browser console for auto-population debug logs")
    print("5. Verify that 'What is your name?' question is auto-filled and readonly")
    
    return True

if __name__ == '__main__':
    main()
