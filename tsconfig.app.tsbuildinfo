{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/composables/usetoast.ts", "./src/constants/states.ts", "./src/router/index.ts", "./src/services/api.ts", "./src/services/xform.ts", "./src/stores/auth.ts", "./src/stores/contacts.ts", "./src/stores/interview.ts", "./src/types/enketo-core.d.ts", "./src/utils/dataimporter.ts", "./src/app.vue", "./src/components/contactform.vue", "./src/components/headerbar.vue", "./src/components/layout.vue", "./src/components/locationselector.vue", "./src/components/navigationmenu.vue", "./src/components/progressindicator.vue", "./src/components/questionrenderer.vue", "./src/components/sidebarnavigation.vue", "./src/components/toastcontainer.vue", "./src/components/toastnotification.vue", "./src/components/xformrenderer.vue", "./src/views/analytics.vue", "./src/views/contacts.vue", "./src/views/dashboard.vue", "./src/views/interview.vue", "./src/views/interviews.vue", "./src/views/login.vue"], "version": "5.8.3"}