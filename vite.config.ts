import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    include: ['enketo-core'],
    exclude: ['leaflet.gridlayer.googlemutant', 'leaflet']
  },
  define: {
    global: 'globalThis',
  },
  assetsInclude: ['**/*.xml'],
  server: {
    fs: {
      allow: ['..']
    }
  }
})
